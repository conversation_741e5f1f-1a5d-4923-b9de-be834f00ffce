{"name": "pokemon-rogue-battle", "private": true, "version": "1.11.2", "type": "module", "scripts": {"start:prod": "vite --mode production", "start:beta": "vite --mode beta", "start:dev": "vite --mode development", "start:podman": "vite --mode development --host 0.0.0.0 --port $PORT", "build": "vite build && node scripts/sync-dist-assets.js", "build:beta": "vite build --mode beta && node scripts/sync-dist-assets.js", "build:dev": "vite build --mode development && node scripts/sync-dist-assets.js", "build:app": "vite build --mode app && node scripts/sync-dist-assets.js", "preview": "vite preview", "test": "vitest run --no-isolate", "test:cov": "vitest run --coverage --no-isolate", "test:watch": "vitest watch --coverage --no-isolate", "test:silent": "vitest run --silent='passed-only' --no-isolate", "test:create": "node scripts/create-test/create-test.js", "eggMoves:parse": "node scripts/parse-egg-moves/main.js", "scrape-trainers": "node scripts/scrape-trainer-names/main.js", "typecheck": "tsc --noEmit", "typecheck:scripts": "tsc -p scripts/jsconfig.json", "biome": "biome check --write --changed --no-errors-on-unmatched --diagnostic-level=error", "biome:all": "biome check --write --no-errors-on-unmatched --diagnostic-level=error", "biome-ci": "biome ci --diagnostic-level=error --reporter=github --no-errors-on-unmatched", "typedoc": "typedoc", "depcruise": "depcruise src test", "postinstall": "lefthook install; git config --local fetch.recurseSubmodules true", "update-version:patch": "pnpm version patch --force --no-git-tag-version", "update-version:minor": "pnpm version minor --force --no-git-tag-version", "update-locales": "git submodule update --progress --init --recursive --depth 1 locales", "update-locales:remote": "git submodule update --progress --init --recursive --force --remote --depth 1 locales", "update-assets": "git submodule update --progress --init --recursive --depth 1 assets", "update-assets:remote": "git submodule update --progress --init --recursive --force --remote --depth 1 assets", "update-submodules": "pnpm update-locales && pnpm update-assets", "update-submodules:remote": "pnpm update-locales:remote && pnpm update-assets:remote"}, "devDependencies": {"@biomejs/biome": "2.2.5", "@ls-lint/ls-lint": "2.3.1", "@types/crypto-js": "^4.2.0", "@types/jsdom": "^27.0.0", "@types/node": "^24", "@vitest/coverage-istanbul": "^3.2.4", "@vitest/expect": "^3.2.4", "@vitest/utils": "^3.2.4", "chalk": "^5.4.1", "dependency-cruiser": "^17.0.2", "inquirer": "^12.8.2", "jsdom": "^27.0.0", "lefthook": "^1.12.2", "msw": "^2.10.4", "phaser3spectorjs": "^0.0.8", "typedoc": "^0.28.13", "typedoc-github-theme": "^0.3.1", "typedoc-plugin-coverage": "^4.0.1", "typedoc-plugin-mdn-links": "^5.0.9", "typescript": "^5.9.2", "vite": "^7.0.7", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vitest-canvas-mock": "^0.3.3"}, "dependencies": {"@material/material-color-utilities": "^0.3.0", "compare-versions": "^6.1.1", "core-js": "^3.46.0", "crypto-js": "^4.2.0", "i18next": "^25.5.3", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "i18next-korean-postposition-processor": "^1.0.0", "json-stable-stringify": "^1.3.0", "jszip": "^3.10.1", "phaser": "^3.90.0", "phaser3-rex-plugins": "^1.80.16"}, "engines": {"node": ">=24.9.0"}, "packageManager": "pnpm@10.19.0"}